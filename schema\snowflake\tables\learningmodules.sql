CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(100),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

ALTER TABLE learningmodules
DROP COLUMN IF EXISTS state;
